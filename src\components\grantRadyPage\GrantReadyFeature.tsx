import React from 'react';

// Placeholder images - you'll need to replace these with actual images
const knowledgeImage = 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80';
const designImage = 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80';

const GrantReadyFeature: React.FC = () => {
  const [isMobile, setIsMobile] = React.useState(false);

  // Track screen size for responsive design
  React.useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <div style={{
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
      backgroundColor: '#FFFFFF',
      padding: isMobile ? '60px 20px' : '80px 40px',
      textAlign: 'center',
      position: 'relative',
    }}>
      {/* Content Wrapper for centering and max-width */}
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        position: 'relative',
      }}>
        {/* Main Heading with GrantReady gradient styling */}
        <h1 style={{
          fontSize: isMobile ? '1.8rem' : '2.5rem',
          fontWeight: 700,
          color: '#000000',
          margin: '0 0 20px 0',
          lineHeight: 1.2,
          letterSpacing: '-0.02em',
        }}>
          International Responder Systems{' '}
          <span style={{
            background: 'linear-gradient(135deg, #6358A6 0%, #4673B7 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
          }}>
            GrantReady™
          </span>{' '}
          Feature
        </h1>

        {/* Subtitle */}
        <p style={{
          fontSize: isMobile ? '1.1rem' : '1.25rem',
          color: '#4B5563',
          fontWeight: 500,
          maxWidth: '800px',
          margin: '0 auto 60px auto',
          lineHeight: 1.6,
        }}>
          Our cloud-based solution offers quality support and monitoring, ensuring your
          grant management is always secure and efficient.
        </p>

        {/* Feature Cards Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr',
          gap: isMobile ? '40px' : '60px',
          alignItems: 'start',
        }}>
          {/* Knowledge Card */}
          <div style={{
            backgroundColor: '#FFFFFF',
            borderRadius: '20px',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
          }}
          >
            {/* Knowledge Image */}
            <div style={{
              width: '100%',
              height: isMobile ? '200px' : '250px',
              backgroundImage: `url(${knowledgeImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }} />
            
            {/* Knowledge Content */}
            <div style={{
              padding: isMobile ? '30px 20px' : '40px 30px',
            }}>
              <h3 style={{
                fontSize: isMobile ? '1.4rem' : '1.6rem',
                fontWeight: 700,
                color: '#000000',
                margin: '0 0 15px 0',
                letterSpacing: '-0.01em',
              }}>
                Knowledge
              </h3>
              <p style={{
                fontSize: isMobile ? '1rem' : '1.1rem',
                color: '#6B7280',
                fontWeight: 500,
                lineHeight: 1.6,
                margin: 0,
              }}>
                Extensive knowledge in government, healthcare,
                engineering disaster & emergency response
              </p>
            </div>
          </div>

          {/* Design Card */}
          <div style={{
            backgroundColor: '#FFFFFF',
            borderRadius: '20px',
            overflow: 'hidden',
            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
            transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-5px)';
            e.currentTarget.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
          }}
          >
            {/* Design Image */}
            <div style={{
              width: '100%',
              height: isMobile ? '200px' : '250px',
              backgroundImage: `url(${designImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
            }} />
            
            {/* Design Content */}
            <div style={{
              padding: isMobile ? '30px 20px' : '40px 30px',
            }}>
              <h3 style={{
                fontSize: isMobile ? '1.4rem' : '1.6rem',
                fontWeight: 700,
                color: '#000000',
                margin: '0 0 15px 0',
                letterSpacing: '-0.01em',
              }}>
                Design
              </h3>
              <p style={{
                fontSize: isMobile ? '1rem' : '1.1rem',
                color: '#6B7280',
                fontWeight: 500,
                lineHeight: 1.6,
                margin: 0,
              }}>
                Intuitive software design for
                flexibility, responsiveness, and automation
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GrantReadyFeature;
